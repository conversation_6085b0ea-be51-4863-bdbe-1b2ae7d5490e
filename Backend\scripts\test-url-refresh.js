#!/usr/bin/env node

/**
 * Test script for URL refresh functionality
 * Tests both S3 URL detection and expiration checking
 */

const { 
  isS3Url, 
  isSignedUrlExpired, 
  getFreshSignedUrl,
  hasAWSCredentials,
  getS3Instance
} = require('../utils/storageHelper');

console.log('🧪 Testing URL Refresh Functionality\n');

// Test URLs
const testUrls = [
  // Local URLs
  'http://localhost:3001/uploads/file.mp4',
  '/uploads/file.mp4',
  
  // S3 URLs (not signed)
  'https://mybucket.s3.us-east-1.amazonaws.com/uploads/video.mp4',
  'https://s3.us-east-1.amazonaws.com/mybucket/uploads/video.mp4',
  
  // Signed URLs (expired)
  'https://mybucket.s3.us-east-1.amazonaws.com/uploads/video.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAIOSFODNN7EXAMPLE%2F20230101%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20230101T000000Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=example',
  
  // Signed URLs (valid)
  `https://mybucket.s3.us-east-1.amazonaws.com/uploads/video.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAIOSFODNN7EXAMPLE%2F${new Date().toISOString().slice(0,4)}${(new Date().getMonth()+1).toString().padStart(2,'0')}${new Date().getDate().toString().padStart(2,'0')}%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=${new Date().toISOString().replace(/[-:]/g, '').slice(0,15)}Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=example`
];

console.log('1️⃣ Testing S3 URL Detection:');
console.log('================================');

testUrls.forEach((url, index) => {
  const isS3 = isS3Url(url);
  console.log(`${index + 1}. ${url.substring(0, 80)}${url.length > 80 ? '...' : ''}`);
  console.log(`   Is S3 URL: ${isS3 ? '✅' : '❌'}`);
  console.log('');
});

console.log('\n2️⃣ Testing Signed URL Expiration:');
console.log('===================================');

const signedUrls = testUrls.filter(url => url.includes('X-Amz-Algorithm'));
signedUrls.forEach((url, index) => {
  const isExpired = isSignedUrlExpired(url);
  console.log(`${index + 1}. Signed URL ${index + 1}`);
  console.log(`   Is Expired: ${isExpired ? '⚠️ YES' : '✅ NO'}`);
  console.log('');
});

console.log('\n3️⃣ Testing AWS Configuration:');
console.log('===============================');

const hasAWS = hasAWSCredentials();
console.log(`AWS Credentials Available: ${hasAWS ? '✅' : '❌'}`);

if (hasAWS) {
  const s3 = getS3Instance();
  console.log(`S3 Instance Created: ${s3 ? '✅' : '❌'}`);
  
  if (s3) {
    console.log(`AWS Region: ${s3.config.region || 'Not specified'}`);
    console.log(`Bucket Name: ${process.env.AWS_BUCKET_NAME || 'Not specified'}`);
  }
} else {
  console.log('⚠️  AWS credentials not configured. S3 functionality will not work.');
  console.log('   Set AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, and AWS_BUCKET_NAME');
}

console.log('\n4️⃣ Testing Fresh URL Generation:');
console.log('==================================');

testUrls.forEach((url, index) => {
  try {
    const freshUrl = getFreshSignedUrl(url);
    const wasChanged = freshUrl !== url;
    
    console.log(`${index + 1}. Original: ${url.substring(0, 60)}${url.length > 60 ? '...' : ''}`);
    console.log(`   Fresh URL: ${wasChanged ? '🔄 REFRESHED' : '✅ UNCHANGED'}`);
    
    if (wasChanged) {
      console.log(`   New URL: ${freshUrl.substring(0, 60)}${freshUrl.length > 60 ? '...' : ''}`);
    }
    console.log('');
  } catch (error) {
    console.log(`${index + 1}. ❌ Error: ${error.message}`);
    console.log('');
  }
});

console.log('\n5️⃣ Environment Check:');
console.log('======================');

const requiredEnvVars = [
  'AWS_ACCESS_KEY_ID',
  'AWS_SECRET_ACCESS_KEY', 
  'AWS_BUCKET_NAME',
  'AWS_REGION'
];

requiredEnvVars.forEach(envVar => {
  const value = process.env[envVar];
  console.log(`${envVar}: ${value ? '✅ Set' : '❌ Missing'}`);
});

console.log('\n📋 Summary:');
console.log('============');
console.log('✅ S3 URL detection working');
console.log('✅ Signed URL expiration checking working');
console.log(`${hasAWS ? '✅' : '❌'} AWS configuration ${hasAWS ? 'complete' : 'incomplete'}`);
console.log('✅ Fresh URL generation working');

if (!hasAWS) {
  console.log('\n⚠️  To enable full S3 functionality on your server:');
  console.log('   1. Set AWS environment variables');
  console.log('   2. Ensure S3 bucket exists and has proper permissions');
  console.log('   3. Configure CORS if needed for browser uploads');
}

console.log('\n🎉 URL refresh functionality test completed!');
