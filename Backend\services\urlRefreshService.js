const { getSignedUrl } = require('../utils/storageHelper');
const { S3_URL_EXPIRATION, S3_PREVIEW_URL_EXPIRATION } = require('../config/timeouts');

/**
 * URL Refresh Service
 * Handles automatic generation and refresh of signed URLs for S3 content
 */
class URLRefreshService {
  constructor() {
    // In-memory cache for signed URLs with expiration tracking
    this.urlCache = new Map();
    this.refreshQueue = new Set();
    
    // Start background refresh process
    this.startBackgroundRefresh();
  }

  /**
   * Generate or refresh a signed URL for an S3 key, or return local URL for local files
   * @param {string} fileKey - S3 object key or local file path
   * @param {string} type - URL type ('content' or 'preview')
   * @param {boolean} forceRefresh - Force generation of new URL
   * @returns {Object} - { url, expiresAt, isNew, isLocal }
   */
  async getOrRefreshSignedUrl(fileKey, type = 'content', forceRefresh = false) {
    try {
      // Check if this is a local file
      const isLocalFile = this.isLocalFile(fileKey);

      if (isLocalFile) {
        // For local files, return the direct URL path
        let localUrl;
        if (fileKey.startsWith('/uploads/')) {
          localUrl = fileKey;
        } else if (fileKey.startsWith('uploads/')) {
          localUrl = `/${fileKey}`;
        } else {
          localUrl = `/uploads/${fileKey}`;
        }

        console.log(`[URLRefresh] Returning local file URL: ${localUrl}`);

        return {
          url: localUrl,
          expiresAt: null, // Local files don't expire
          isNew: false,
          isLocal: true,
          timeUntilExpiry: null
        };
      }

      // For S3 files, proceed with signed URL generation
      const cacheKey = `${fileKey}:${type}`;
      const now = new Date();
      
      // Check if we have a valid cached URL
      if (!forceRefresh && this.urlCache.has(cacheKey)) {
        const cached = this.urlCache.get(cacheKey);
        const timeUntilExpiry = cached.expiresAt - now;
        
        // If URL expires in more than 10 minutes, return cached version
        if (timeUntilExpiry > 10 * 60 * 1000) {
          return {
            url: cached.url,
            expiresAt: cached.expiresAt,
            isNew: false,
            timeUntilExpiry: Math.floor(timeUntilExpiry / 1000)
          };
        }
      }

      // Generate new signed URL
      const expirationTime = type === 'preview' ? S3_PREVIEW_URL_EXPIRATION : S3_URL_EXPIRATION;
      const signedUrl = getSignedUrl(fileKey, expirationTime);
      const expiresAt = new Date(now.getTime() + (expirationTime * 1000));

      // Cache the new URL
      this.urlCache.set(cacheKey, {
        url: signedUrl,
        expiresAt: expiresAt,
        s3Key: fileKey,
        type: type,
        createdAt: now
      });

      console.log(`[URLRefresh] Generated new signed URL for ${fileKey} (${type}), expires at ${expiresAt.toISOString()}`);

      return {
        url: signedUrl,
        expiresAt: expiresAt,
        isNew: true,
        isLocal: false,
        timeUntilExpiry: expirationTime
      };

    } catch (error) {
      console.error(`[URLRefresh] Error generating signed URL for ${fileKey}:`, error);
      throw error;
    }
  }

  /**
   * Check if a file key represents a local file
   * @param {string} fileKey - File key to check
   * @returns {boolean} - Whether the file is local
   */
  isLocalFile(fileKey) {
    if (!fileKey) return false;

    // Local files typically:
    // - Start with 'uploads/'
    // - Don't contain amazonaws.com or s3.
    // - Don't start with https:// (unless it's a local server URL)
    return fileKey.startsWith('uploads/') ||
           (!fileKey.includes('amazonaws.com') &&
            !fileKey.includes('s3.') &&
            !fileKey.startsWith('https://'));
  }

  /**
   * Refresh multiple URLs at once
   * @param {Array} s3Keys - Array of S3 keys to refresh
   * @param {string} type - URL type
   * @returns {Object} - Map of s3Key to URL data
   */
  async refreshMultipleUrls(s3Keys, type = 'content') {
    const results = {};
    
    for (const s3Key of s3Keys) {
      try {
        results[s3Key] = await this.getOrRefreshSignedUrl(s3Key, type, true);
      } catch (error) {
        console.error(`[URLRefresh] Failed to refresh URL for ${s3Key}:`, error);
        results[s3Key] = { error: error.message };
      }
    }

    return results;
  }

  /**
   * Check if a URL needs refresh based on expiration time
   * @param {string} s3Key - S3 object key
   * @param {string} type - URL type
   * @returns {boolean} - Whether URL needs refresh
   */
  needsRefresh(s3Key, type = 'content') {
    const cacheKey = `${s3Key}:${type}`;
    
    if (!this.urlCache.has(cacheKey)) {
      return true;
    }

    const cached = this.urlCache.get(cacheKey);
    const now = new Date();
    const timeUntilExpiry = cached.expiresAt - now;

    // Refresh if expires in less than 15 minutes
    return timeUntilExpiry < 15 * 60 * 1000;
  }

  /**
   * Get cached URL info without generating new one
   * @param {string} s3Key - S3 object key
   * @param {string} type - URL type
   * @returns {Object|null} - Cached URL data or null
   */
  getCachedUrl(s3Key, type = 'content') {
    const cacheKey = `${s3Key}:${type}`;
    return this.urlCache.get(cacheKey) || null;
  }

  /**
   * Start background process to refresh URLs before they expire
   */
  startBackgroundRefresh() {
    // Run every 5 minutes
    setInterval(() => {
      this.performBackgroundRefresh();
    }, 5 * 60 * 1000);

    console.log('[URLRefresh] Background refresh service started');
  }

  /**
   * Background refresh process
   */
  async performBackgroundRefresh() {
    const now = new Date();
    const urlsToRefresh = [];

    // Find URLs that expire in the next 20 minutes
    for (const [cacheKey, cached] of this.urlCache.entries()) {
      const timeUntilExpiry = cached.expiresAt - now;
      
      if (timeUntilExpiry < 20 * 60 * 1000 && timeUntilExpiry > 0) {
        urlsToRefresh.push({
          s3Key: cached.s3Key,
          type: cached.type,
          cacheKey: cacheKey
        });
      }
    }

    if (urlsToRefresh.length > 0) {
      console.log(`[URLRefresh] Background refresh: refreshing ${urlsToRefresh.length} URLs`);
      
      for (const item of urlsToRefresh) {
        try {
          await this.getOrRefreshSignedUrl(item.s3Key, item.type, true);
        } catch (error) {
          console.error(`[URLRefresh] Background refresh failed for ${item.s3Key}:`, error);
        }
      }
    }

    // Clean up expired URLs from cache
    this.cleanupExpiredUrls();
  }

  /**
   * Remove expired URLs from cache
   */
  cleanupExpiredUrls() {
    const now = new Date();
    let cleanedCount = 0;

    for (const [cacheKey, cached] of this.urlCache.entries()) {
      if (cached.expiresAt < now) {
        this.urlCache.delete(cacheKey);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`[URLRefresh] Cleaned up ${cleanedCount} expired URLs from cache`);
    }
  }

  /**
   * Get cache statistics
   * @returns {Object} - Cache statistics
   */
  getCacheStats() {
    const now = new Date();
    let activeUrls = 0;
    let expiringSoon = 0;

    for (const cached of this.urlCache.values()) {
      if (cached.expiresAt > now) {
        activeUrls++;
        const timeUntilExpiry = cached.expiresAt - now;
        if (timeUntilExpiry < 30 * 60 * 1000) { // 30 minutes
          expiringSoon++;
        }
      }
    }

    return {
      totalCached: this.urlCache.size,
      activeUrls: activeUrls,
      expiringSoon: expiringSoon,
      queueSize: this.refreshQueue.size
    };
  }

  /**
   * Clear all cached URLs (useful for testing)
   */
  clearCache() {
    this.urlCache.clear();
    this.refreshQueue.clear();
    console.log('[URLRefresh] Cache cleared');
  }
}

// Create singleton instance
const urlRefreshService = new URLRefreshService();

module.exports = urlRefreshService;
