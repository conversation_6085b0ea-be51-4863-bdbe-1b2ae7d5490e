import React, { useState } from 'react';
import { getFreshUrl, refreshSignedUrl, urlNeedsRefresh } from '../../utils/constants';

/**
 * Test component for URL refresh functionality
 * Use this to test URL refresh in development
 */
const UrlRefreshTest = () => {
  const [testUrl, setTestUrl] = useState('');
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Sample test URLs
  const sampleUrls = [
    'https://mybucket.s3.us-east-1.amazonaws.com/uploads/video.mp4',
    'http://localhost:3001/uploads/video.mp4',
    '/uploads/video.mp4'
  ];

  const testFreshUrl = async () => {
    if (!testUrl.trim()) {
      setError('Please enter a URL to test');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const startTime = Date.now();
      const freshUrl = await getFreshUrl(testUrl, 'content');
      const endTime = Date.now();

      setResult({
        type: 'fresh',
        originalUrl: testUrl,
        freshUrl,
        wasChanged: freshUrl !== testUrl,
        responseTime: endTime - startTime,
        needsRefresh: urlNeedsRefresh(testUrl)
      });
    } catch (err) {
      setError(`Error getting fresh URL: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testForceRefresh = async () => {
    if (!testUrl.trim()) {
      setError('Please enter a URL to test');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const startTime = Date.now();
      const refreshedUrl = await refreshSignedUrl(testUrl, 'content');
      const endTime = Date.now();

      setResult({
        type: 'force_refresh',
        originalUrl: testUrl,
        refreshedUrl,
        wasChanged: refreshedUrl !== testUrl,
        responseTime: endTime - startTime,
        needsRefresh: urlNeedsRefresh(testUrl)
      });
    } catch (err) {
      setError(`Error force refreshing URL: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const useSampleUrl = (url) => {
    setTestUrl(url);
    setResult(null);
    setError(null);
  };

  return (
    <div style={{ 
      padding: '20px', 
      border: '1px solid #ddd', 
      borderRadius: '8px', 
      margin: '20px',
      backgroundColor: '#f9f9f9'
    }}>
      <h3>🧪 URL Refresh Test Component</h3>
      
      <div style={{ marginBottom: '15px' }}>
        <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
          Test URL:
        </label>
        <input
          type="text"
          value={testUrl}
          onChange={(e) => setTestUrl(e.target.value)}
          placeholder="Enter URL to test..."
          style={{
            width: '100%',
            padding: '8px',
            border: '1px solid #ccc',
            borderRadius: '4px',
            fontSize: '14px'
          }}
        />
      </div>

      <div style={{ marginBottom: '15px' }}>
        <strong>Sample URLs:</strong>
        <div style={{ marginTop: '5px' }}>
          {sampleUrls.map((url, index) => (
            <button
              key={index}
              onClick={() => useSampleUrl(url)}
              style={{
                margin: '2px',
                padding: '4px 8px',
                fontSize: '12px',
                backgroundColor: '#e0e0e0',
                border: '1px solid #ccc',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              {url.length > 40 ? `${url.substring(0, 40)}...` : url}
            </button>
          ))}
        </div>
      </div>

      <div style={{ marginBottom: '15px' }}>
        <button
          onClick={testFreshUrl}
          disabled={loading}
          style={{
            marginRight: '10px',
            padding: '10px 15px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: loading ? 'not-allowed' : 'pointer',
            opacity: loading ? 0.6 : 1
          }}
        >
          {loading ? 'Testing...' : 'Test Fresh URL'}
        </button>
        
        <button
          onClick={testForceRefresh}
          disabled={loading}
          style={{
            padding: '10px 15px',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: loading ? 'not-allowed' : 'pointer',
            opacity: loading ? 0.6 : 1
          }}
        >
          {loading ? 'Testing...' : 'Force Refresh'}
        </button>
      </div>

      {error && (
        <div style={{
          padding: '10px',
          backgroundColor: '#f8d7da',
          color: '#721c24',
          border: '1px solid #f5c6cb',
          borderRadius: '4px',
          marginBottom: '15px'
        }}>
          ❌ {error}
        </div>
      )}

      {result && (
        <div style={{
          padding: '15px',
          backgroundColor: '#d4edda',
          color: '#155724',
          border: '1px solid #c3e6cb',
          borderRadius: '4px'
        }}>
          <h4>✅ Test Result ({result.type})</h4>
          <div style={{ fontSize: '14px', fontFamily: 'monospace' }}>
            <div><strong>Original URL:</strong> {result.originalUrl}</div>
            <div><strong>Result URL:</strong> {result.freshUrl || result.refreshedUrl}</div>
            <div><strong>Was Changed:</strong> {result.wasChanged ? '🔄 YES' : '✅ NO'}</div>
            <div><strong>Needs Refresh:</strong> {result.needsRefresh ? '⚠️ YES' : '✅ NO'}</div>
            <div><strong>Response Time:</strong> {result.responseTime}ms</div>
          </div>
        </div>
      )}

      <div style={{ 
        marginTop: '15px', 
        padding: '10px', 
        backgroundColor: '#e2e3e5', 
        borderRadius: '4px',
        fontSize: '12px'
      }}>
        <strong>How to use:</strong>
        <ul style={{ margin: '5px 0', paddingLeft: '20px' }}>
          <li><strong>Fresh URL:</strong> Checks if URL needs refresh and refreshes only if needed</li>
          <li><strong>Force Refresh:</strong> Always generates a new signed URL</li>
          <li>Local URLs will be returned as-is</li>
          <li>S3 URLs will be converted to signed URLs</li>
          <li>Expired signed URLs will be refreshed</li>
        </ul>
      </div>
    </div>
  );
};

export default UrlRefreshTest;
